# PowerShell script to count exact number of data rows in SQL files
# This script counts actual data rows by looking for INSERT VALUE patterns

Write-Host "=== SQL ROW COUNT VERIFICATION ==="
Write-Host ""

function Count-SqlRows {
    param([string]$FilePath)
    
    Write-Host "Counting rows in: $FilePath"
    
    if (-not (Test-Path $FilePath)) {
        Write-Host "  ERROR: File not found!" -ForegroundColor Red
        return 0
    }
    
    $content = Get-Content $FilePath
    $rowCount = 0
    $inInsertBlock = $false
    
    foreach ($line in $content) {
        # Check if we're starting an INSERT block
        if ($line -match "INSERT INTO.*VALUES") {
            $inInsertBlock = $true
            # Count rows in the same line if VALUES is followed by data
            if ($line -match "VALUES\s*\(") {
                $matches = [regex]::Matches($line, '\((?:[^()]*|\([^)]*\))*\)')
                $rowCount += $matches.Count
            }
        }
        elseif ($inInsertBlock) {
            # Check if this line contains data rows (starts with opening parenthesis)
            if ($line -match '^\s*\(') {
                # Count the number of complete row patterns in this line
                $matches = [regex]::Matches($line, '\((?:[^()]*|\([^)]*\))*\)')
                $rowCount += $matches.Count
            }
            # Check if we've reached the end of INSERT block
            elseif ($line -match '^--' -or $line -match 'COMMIT' -or $line -match '^SET' -or $line -match '^/\*!' -or $line.Trim() -eq "") {
                $inInsertBlock = $false
            }
        }
    }
    
    Write-Host "  Rows found: $rowCount" -ForegroundColor Green
    return $rowCount
}

# Get all original SQL files (excluding the merged one)
$originalFiles = Get-ChildItem -Path "*.sql" | Where-Object { $_.Name -ne "merged_gazette_notices.sql" }
$mergedFile = "merged_gazette_notices.sql"

Write-Host "Original files to check:"
foreach ($file in $originalFiles) {
    Write-Host "  - $($file.Name)"
}
Write-Host ""

# Count rows in each original file
$totalOriginalRows = 0
$fileCounts = @{}

foreach ($file in $originalFiles) {
    $count = Count-SqlRows -FilePath $file.FullName
    $fileCounts[$file.Name] = $count
    $totalOriginalRows += $count
}

Write-Host ""
Write-Host "=== ORIGINAL FILES SUMMARY ===" -ForegroundColor Yellow
foreach ($file in $fileCounts.Keys | Sort-Object) {
    Write-Host "  $file : $($fileCounts[$file]) rows"
}
Write-Host "  TOTAL ORIGINAL ROWS: $totalOriginalRows" -ForegroundColor Cyan
Write-Host ""

# Count rows in merged file
Write-Host "=== MERGED FILE CHECK ===" -ForegroundColor Yellow
if (Test-Path $mergedFile) {
    $mergedRows = Count-SqlRows -FilePath $mergedFile
    Write-Host "  MERGED FILE ROWS: $mergedRows" -ForegroundColor Cyan
    Write-Host ""
    
    # Compare totals
    Write-Host "=== VERIFICATION RESULT ===" -ForegroundColor Magenta
    if ($mergedRows -eq $totalOriginalRows) {
        Write-Host "  ✅ SUCCESS: Row counts match perfectly!" -ForegroundColor Green
        Write-Host "  Original files total: $totalOriginalRows rows"
        Write-Host "  Merged file total: $mergedRows rows"
        Write-Host "  Difference: 0 rows"
    } else {
        Write-Host "  ❌ ERROR: Row counts do not match!" -ForegroundColor Red
        Write-Host "  Original files total: $totalOriginalRows rows"
        Write-Host "  Merged file total: $mergedRows rows"
        Write-Host "  Difference: $($totalOriginalRows - $mergedRows) rows"
        
        if ($mergedRows -lt $totalOriginalRows) {
            Write-Host "  ⚠️  Data may have been lost during merge!" -ForegroundColor Red
        } else {
            Write-Host "  ⚠️  Extra data may have been added during merge!" -ForegroundColor Red
        }
    }
} else {
    Write-Host "  ❌ ERROR: Merged file not found!" -ForegroundColor Red
}

Write-Host ""
Write-Host "Row count verification completed."
